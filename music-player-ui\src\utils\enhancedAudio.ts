/**
 * Enhanced Audio Element
 * 
 * This module provides an enhanced audio element that can handle both regular audio formats
 * and FLAC files using the FLAC decoder when needed.
 */

import { FlacPlayer, FlacPlayerOptions, isFlacPlaybackSupported } from './flacPlayer';
import { getAudioFormatFromUrl, isAudioFormatSupported } from './audioSupport';

export interface EnhancedAudioOptions {
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: Error) => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onEnded?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  onCanPlay?: () => void;
  onLoadedMetadata?: () => void;
}

export class EnhancedAudio {
  private audioElement: HTMLAudioElement | null = null;
  private flacPlayer: FlacPlayer | null = null;
  private isFlacMode: boolean = false;
  private options: EnhancedAudioOptions;
  private _src: string = '';
  private _volume: number = 1;
  private _currentTime: number = 0;
  private _duration: number = 0;
  private _paused: boolean = true;
  private _ended: boolean = false;

  constructor(options: EnhancedAudioOptions = {}) {
    this.options = options;
  }

  get src(): string {
    return this._src;
  }

  set src(url: string) {
    this._src = url;
    this.loadAudio(url);
  }

  get volume(): number {
    return this._volume;
  }

  set volume(value: number) {
    this._volume = Math.max(0, Math.min(1, value));
    if (this.isFlacMode && this.flacPlayer) {
      this.flacPlayer.setVolume(this._volume);
    } else if (this.audioElement) {
      this.audioElement.volume = this._volume;
    }
  }

  get currentTime(): number {
    if (this.isFlacMode && this.flacPlayer) {
      return this.flacPlayer.getCurrentTime();
    } else if (this.audioElement) {
      return this.audioElement.currentTime;
    }
    return this._currentTime;
  }

  set currentTime(time: number) {
    if (this.isFlacMode && this.flacPlayer) {
      this.flacPlayer.seek(time);
    } else if (this.audioElement) {
      this.audioElement.currentTime = time;
    }
    this._currentTime = time;
  }

  get duration(): number {
    if (this.isFlacMode && this.flacPlayer) {
      return this.flacPlayer.getDuration();
    } else if (this.audioElement) {
      return this.audioElement.duration || 0;
    }
    return this._duration;
  }

  get paused(): boolean {
    if (this.isFlacMode && this.flacPlayer) {
      return !this.flacPlayer.getIsPlaying();
    } else if (this.audioElement) {
      return this.audioElement.paused;
    }
    return this._paused;
  }

  get ended(): boolean {
    if (this.isFlacMode && this.flacPlayer) {
      return !this.flacPlayer.getIsPlaying() && !this.flacPlayer.getIsPaused() && this.currentTime >= this.duration;
    } else if (this.audioElement) {
      return this.audioElement.ended;
    }
    return this._ended;
  }

  private async loadAudio(url: string): Promise<void> {
    try {
      // Clean up previous audio
      this.cleanup();

      console.log('🎵 Loading audio:', url);

      // Determine if this is a FLAC file
      const format = getAudioFormatFromUrl(url);
      const isFlac = format === 'flac';
      const isNativelySupported = format ? isAudioFormatSupported(format) : true;

      console.log(`🔍 Audio format: ${format}, natively supported: ${isNativelySupported}`);

      if (isFlac && !isNativelySupported && isFlacPlaybackSupported()) {
        // Use FLAC decoder
        console.log('🎼 Using FLAC decoder for playback');
        this.isFlacMode = true;
        await this.loadFlacAudio(url);
      } else {
        // Use regular HTML audio element
        console.log('🎵 Using HTML audio element for playback');
        this.isFlacMode = false;
        this.loadRegularAudio(url);
      }
    } catch (error) {
      console.error('❌ Failed to load audio:', error);
      this.options.onError?.(error as Error);
    }
  }

  private async loadFlacAudio(url: string): Promise<void> {
    const flacOptions: FlacPlayerOptions = {
      onLoadStart: () => {
        console.log('🔄 FLAC loading started');
        this.options.onLoadStart?.();
      },
      onLoadEnd: () => {
        console.log('✅ FLAC loading completed');
        this._duration = this.flacPlayer?.getDuration() || 0;
        this.options.onLoadEnd?.();
        this.options.onCanPlay?.();
        this.options.onLoadedMetadata?.();
      },
      onError: (error) => {
        console.error('❌ FLAC playback error:', error);
        this.options.onError?.(error);
      },
      onTimeUpdate: (currentTime, duration) => {
        this._currentTime = currentTime;
        this._duration = duration;
        this.options.onTimeUpdate?.(currentTime, duration);
      },
      onEnded: () => {
        console.log('🏁 FLAC playback ended');
        this._ended = true;
        this._paused = true;
        this.options.onEnded?.();
      },
      onPlay: () => {
        console.log('▶️ FLAC playback started');
        this._paused = false;
        this._ended = false;
        this.options.onPlay?.();
      },
      onPause: () => {
        console.log('⏸️ FLAC playback paused');
        this._paused = true;
        this.options.onPause?.();
      }
    };

    this.flacPlayer = new FlacPlayer(flacOptions);
    await this.flacPlayer.loadFlacFromUrl(url);
  }

  private loadRegularAudio(url: string): void {
    this.audioElement = new Audio();
    
    // Set up event listeners
    this.audioElement.addEventListener('loadstart', () => {
      console.log('🔄 Audio loading started');
      this.options.onLoadStart?.();
    });

    this.audioElement.addEventListener('canplay', () => {
      console.log('✅ Audio can play');
      this.options.onCanPlay?.();
    });

    this.audioElement.addEventListener('loadedmetadata', () => {
      console.log('📊 Audio metadata loaded');
      this._duration = this.audioElement?.duration || 0;
      this.options.onLoadedMetadata?.();
    });

    this.audioElement.addEventListener('loadeddata', () => {
      console.log('✅ Audio data loaded');
      this.options.onLoadEnd?.();
    });

    this.audioElement.addEventListener('timeupdate', () => {
      if (this.audioElement) {
        this._currentTime = this.audioElement.currentTime;
        this._duration = this.audioElement.duration || 0;
        this.options.onTimeUpdate?.(this._currentTime, this._duration);
      }
    });

    this.audioElement.addEventListener('ended', () => {
      console.log('🏁 Audio playback ended');
      this._ended = true;
      this._paused = true;
      this.options.onEnded?.();
    });

    this.audioElement.addEventListener('play', () => {
      console.log('▶️ Audio playback started');
      this._paused = false;
      this._ended = false;
      this.options.onPlay?.();
    });

    this.audioElement.addEventListener('pause', () => {
      console.log('⏸️ Audio playback paused');
      this._paused = true;
      this.options.onPause?.();
    });

    this.audioElement.addEventListener('error', (event) => {
      const error = this.audioElement?.error;
      console.error('❌ Audio playback error:', error);
      this.options.onError?.(new Error(error?.message || 'Audio playback failed'));
    });

    // Set properties
    this.audioElement.volume = this._volume;
    this.audioElement.src = url;
  }

  async play(): Promise<void> {
    try {
      if (this.isFlacMode && this.flacPlayer) {
        this.flacPlayer.play();
      } else if (this.audioElement) {
        await this.audioElement.play();
      }
    } catch (error) {
      console.error('❌ Failed to play audio:', error);
      this.options.onError?.(error as Error);
    }
  }

  pause(): void {
    if (this.isFlacMode && this.flacPlayer) {
      this.flacPlayer.pause();
    } else if (this.audioElement) {
      this.audioElement.pause();
    }
  }

  stop(): void {
    if (this.isFlacMode && this.flacPlayer) {
      this.flacPlayer.stop();
    } else if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.currentTime = 0;
    }
  }

  private cleanup(): void {
    if (this.flacPlayer) {
      this.flacPlayer.destroy();
      this.flacPlayer = null;
    }
    if (this.audioElement) {
      this.audioElement.pause();
      this.audioElement.src = '';
      this.audioElement = null;
    }
    this.isFlacMode = false;
    this._paused = true;
    this._ended = false;
    this._currentTime = 0;
    this._duration = 0;
  }

  destroy(): void {
    this.cleanup();
    console.log('🗑️ Enhanced audio destroyed');
  }
}

/**
 * Create an enhanced audio element that can handle FLAC files
 */
export const createEnhancedAudio = (options: EnhancedAudioOptions = {}): EnhancedAudio => {
  return new EnhancedAudio(options);
};
