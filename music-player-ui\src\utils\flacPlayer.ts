/**
 * FLAC Audio Player Utility
 *
 * This module provides FLAC playback support for browsers that don't natively support FLAC.
 * It uses @wasm-audio-decoders/flac to decode FLAC files and Web Audio API for playback.
 */

// Import WASM FLAC decoder
import { FLACDecoder } from "@wasm-audio-decoders/flac";

export interface FlacPlayerOptions {
  onLoadStart?: () => void;
  onLoadEnd?: () => void;
  onError?: (error: Error) => void;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onEnded?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
}

export class FlacPlayer {
  private audioContext: AudioContext | null = null;
  private audioBuffer: AudioBuffer | null = null;
  private sourceNode: AudioBufferSourceNode | null = null;
  private gainNode: GainNode | null = null;
  private startTime: number = 0;
  private pauseTime: number = 0;
  private isPlaying: boolean = false;
  private isPaused: boolean = false;
  private duration: number = 0;
  private currentTime: number = 0;
  private volume: number = 1;
  private options: FlacPlayerOptions;
  private timeUpdateInterval: number | null = null;

  constructor(options: FlacPlayerOptions = {}) {
    this.options = options;
    this.initAudioContext();
  }

  private initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.audioContext.destination);
    } catch (error) {
      console.error("Failed to initialize AudioContext:", error);
      this.options.onError?.(new Error("Web Audio API not supported"));
    }
  }

  async loadFlacFromUrl(url: string): Promise<void> {
    if (!this.audioContext) {
      throw new Error("AudioContext not initialized");
    }

    this.options.onLoadStart?.();

    try {
      console.log("🎵 Loading FLAC file:", url);

      // Fetch the FLAC file
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch FLAC file: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      console.log(
        "📦 FLAC file loaded, size:",
        arrayBuffer.byteLength,
        "bytes"
      );

      // Decode FLAC using WASM decoder
      const flacDecoder = new FLACDecoder();
      const decoded = await flacDecoder.decode(new Uint8Array(arrayBuffer));

      console.log("🔧 FLAC decoded:", {
        sampleRate: decoded.sampleRate,
        numberOfChannels: decoded.numberOfChannels,
        length: decoded.length,
        duration: decoded.duration,
      });

      // The WASM decoder returns an AudioBuffer-like object
      // Convert to actual AudioBuffer
      this.audioBuffer = this.audioContext.createBuffer(
        decoded.numberOfChannels,
        decoded.length,
        decoded.sampleRate
      );

      // Copy the decoded audio data
      for (let channel = 0; channel < decoded.numberOfChannels; channel++) {
        const channelData = this.audioBuffer.getChannelData(channel);
        const sourceData = decoded.getChannelData(channel);
        channelData.set(sourceData);
      }

      this.duration = this.audioBuffer.duration;
      console.log(
        "✅ FLAC audio buffer created, duration:",
        this.duration,
        "seconds"
      );

      this.options.onLoadEnd?.();
    } catch (error) {
      console.error("❌ Failed to load FLAC file:", error);
      this.options.onError?.(error as Error);
      throw error;
    }
  }

  play(): void {
    if (!this.audioContext || !this.audioBuffer) {
      console.warn("Cannot play: AudioContext or AudioBuffer not ready");
      return;
    }

    if (this.audioContext.state === "suspended") {
      this.audioContext.resume();
    }

    // Stop any currently playing audio
    this.stop();

    // Create a new source node
    this.sourceNode = this.audioContext.createBufferSource();
    this.sourceNode.buffer = this.audioBuffer;
    this.sourceNode.connect(this.gainNode!);

    // Set up event handlers
    this.sourceNode.onended = () => {
      this.isPlaying = false;
      this.isPaused = false;
      this.currentTime = 0;
      this.stopTimeUpdate();
      this.options.onEnded?.();
    };

    // Start playback
    const offset = this.isPaused ? this.pauseTime : 0;
    this.sourceNode.start(0, offset);
    this.startTime = this.audioContext.currentTime - offset;
    this.isPlaying = true;
    this.isPaused = false;

    this.startTimeUpdate();
    this.options.onPlay?.();

    console.log("▶️ FLAC playback started at offset:", offset);
  }

  pause(): void {
    if (this.isPlaying && this.sourceNode) {
      this.sourceNode.stop();
      this.pauseTime = this.audioContext!.currentTime - this.startTime;
      this.isPlaying = false;
      this.isPaused = true;
      this.stopTimeUpdate();
      this.options.onPause?.();
      console.log("⏸️ FLAC playback paused at:", this.pauseTime);
    }
  }

  stop(): void {
    if (this.sourceNode) {
      this.sourceNode.stop();
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }
    this.isPlaying = false;
    this.isPaused = false;
    this.pauseTime = 0;
    this.currentTime = 0;
    this.stopTimeUpdate();
    console.log("⏹️ FLAC playback stopped");
  }

  seek(time: number): void {
    if (!this.audioBuffer) return;

    const wasPlaying = this.isPlaying;
    this.stop();
    this.pauseTime = Math.max(0, Math.min(time, this.duration));

    if (wasPlaying) {
      this.play();
    } else {
      this.isPaused = true;
      this.currentTime = this.pauseTime;
    }

    console.log("⏭️ FLAC seeked to:", this.pauseTime);
  }

  setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
    if (this.gainNode) {
      this.gainNode.gain.value = this.volume;
    }
  }

  getVolume(): number {
    return this.volume;
  }

  getCurrentTime(): number {
    if (this.isPlaying && this.audioContext) {
      return this.audioContext.currentTime - this.startTime;
    }
    return this.currentTime;
  }

  getDuration(): number {
    return this.duration;
  }

  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  getIsPaused(): boolean {
    return this.isPaused;
  }

  private startTimeUpdate(): void {
    this.stopTimeUpdate();
    this.timeUpdateInterval = window.setInterval(() => {
      if (this.isPlaying) {
        this.currentTime = this.getCurrentTime();
        this.options.onTimeUpdate?.(this.currentTime, this.duration);
      }
    }, 100); // Update every 100ms
  }

  private stopTimeUpdate(): void {
    if (this.timeUpdateInterval) {
      clearInterval(this.timeUpdateInterval);
      this.timeUpdateInterval = null;
    }
  }

  destroy(): void {
    this.stop();
    this.stopTimeUpdate();
    if (this.gainNode) {
      this.gainNode.disconnect();
    }
    if (this.audioContext && this.audioContext.state !== "closed") {
      this.audioContext.close();
    }
    console.log("🗑️ FLAC player destroyed");
  }
}

/**
 * Check if FLAC playback is supported (either natively or via decoder)
 */
export const isFlacPlaybackSupported = (): boolean => {
  // Check for Web Audio API support (required for our FLAC decoder)
  const hasWebAudio = !!(
    window.AudioContext || (window as any).webkitAudioContext
  );

  if (!hasWebAudio) {
    console.warn("Web Audio API not supported - FLAC playback not available");
    return false;
  }

  console.log("✅ FLAC playback supported via Web Audio API + WASM decoder");
  return true;
};

/**
 * Create a FLAC-capable audio element wrapper
 */
export const createFlacAudioElement = (
  src: string,
  options: FlacPlayerOptions = {}
): FlacPlayer => {
  const player = new FlacPlayer(options);

  // Auto-load the FLAC file
  player.loadFlacFromUrl(src).catch((error) => {
    console.error("Failed to load FLAC file:", error);
    options.onError?.(error);
  });

  return player;
};
